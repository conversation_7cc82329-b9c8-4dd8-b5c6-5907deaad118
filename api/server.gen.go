// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package api

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// 健康检查
	// (GET /api)
	HealthCheck(c *gin.Context)
	// 创建支付交易
	// (POST /api/payments)
	CreatePayment(c *gin.Context)
	// 查询交易状态
	// (GET /api/payments/{transaction_hash})
	GetTransactionStatus(c *gin.Context, transactionHash string)
	// 查询用户限制
	// (GET /api/users/{user_address}/limits)
	GetUserLimits(c *gin.Context, userAddress string)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// HealthCheck operation middleware
func (siw *ServerInterfaceWrapper) HealthCheck(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.HealthCheck(c)
}

// CreatePayment operation middleware
func (siw *ServerInterfaceWrapper) CreatePayment(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreatePayment(c)
}

// GetTransactionStatus operation middleware
func (siw *ServerInterfaceWrapper) GetTransactionStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "transaction_hash" -------------
	var transactionHash string

	err = runtime.BindStyledParameterWithOptions("simple", "transaction_hash", c.Param("transaction_hash"), &transactionHash, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter transaction_hash: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTransactionStatus(c, transactionHash)
}

// GetUserLimits operation middleware
func (siw *ServerInterfaceWrapper) GetUserLimits(c *gin.Context) {

	var err error

	// ------------- Path parameter "user_address" -------------
	var userAddress string

	err = runtime.BindStyledParameterWithOptions("simple", "user_address", c.Param("user_address"), &userAddress, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter user_address: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUserLimits(c, userAddress)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/api", wrapper.HealthCheck)
	router.POST(options.BaseURL+"/api/payments", wrapper.CreatePayment)
	router.GET(options.BaseURL+"/api/payments/:transaction_hash", wrapper.GetTransactionStatus)
	router.GET(options.BaseURL+"/api/users/:user_address/limits", wrapper.GetUserLimits)
}

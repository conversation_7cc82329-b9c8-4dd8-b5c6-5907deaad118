// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package api

// Defines values for PaymentRequestCurrency.
const (
	APT  PaymentRequestCurrency = "APT"
	USDC PaymentRequestCurrency = "USDC"
)

// ApiResponse defines model for ApiResponse.
type ApiResponse struct {
	// Code 业务状态码
	Code int `json:"code"`

	// Data 响应数据
	Data *map[string]interface{} `json:"data"`
}

// PaymentRequest defines model for PaymentRequest.
type PaymentRequest struct {
	// Amount 金额 uint类型
	Amount int64 `json:"amount"`

	// Currency 货币种类
	Currency *PaymentRequestCurrency `json:"currency,omitempty"`

	// Opt OPT hex格式
	Opt string `json:"opt"`

	// PayeeAddr 收款地址 hex格式
	PayeeAddr string `json:"payee_addr"`

	// PayerAddr 付款地址 hex格式
	PayerAddr string `json:"payer_addr"`
}

// PaymentRequestCurrency 货币种类
type PaymentRequestCurrency string

// CreatePaymentJSONRequestBody defines body for CreatePayment for application/json ContentType.
type CreatePaymentJSONRequestBody = PaymentRequest

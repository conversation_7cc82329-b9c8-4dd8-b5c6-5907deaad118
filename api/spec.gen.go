// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/7xY23ITRxp+lakmF7tZWWeZte7YJJtQtVtxgXOFWLk9auHJSj2TOVDWulQlB3sjQDZO",
	"MCdjYgwmdu1iyxTEKLKAh4m6Z3zlV9jq7pE0mhnDeiF7ZatP/9f/4fu/nlkgq2VNxQibBsjOAkOeRmXI",
	"/z2jKeeQoanYQOynpqsa0k0F8UlZLfDRAjJkXdFMRcUgC7qtVXJtw762T2tz9sM5EAFoBpa1EgLZRDwe",
	"jwCzoiGQBQo20SWkg2oEFKAJgweRm4ukvUJv7dHFXRAB2CqV4BQ7xtQt1D9GnfoaySaoViNAR99Yio4K",
	"IHtBYLsYWBUB47BSRtg8h76xkGEGLwXLqoXNIJrD774/fPRAshRs2s8OyI/X/Rfjdyuqehma4najaRAB",
	"ZQUrZasMsomwi8uWriMsV4S5IrRKbO+Z8QkQ8Zl3XmyR1py9tWg/O2CWMTvzgrv0q/OffsIu28fjjrsG",
	"DVNX8CVmT9VCbvbl+IQ0jWboww7p3PDeChQQLEwhVAQRoEHTRDpb/rcLcKR4ZuTP8ZGxi3/4KMyKBisI",
	"5WGhoAeN0ZV9+vQ1WdsjD2rHWI3PwCm5gIqJZCqdGT39x7H4cb+HccVnPMhmE5HRdPVYePox8LoHd98N",
	"793AxO//BZ4vjT1YRfSGvBvpZWsw0dlBCi6qok6xCWUeeQzLbNWEgivjsCKdGT8rnbc0TdV5bfjqb73d",
	"bS9J5z47P1G0SpKzNe80rtir83Slydy09ITceHzUadCVJm3MSWc0UzUk0miTB3cOb77utjfp3Ztkc95e",
	"/uevtW9zOIdPnZLckuZOdZov6N2lHKZXa3Ttqjiu++qNvbJtH6x3WzV7dd67/KizmsOTk5NfGyrO4dkc",
	"lqQcr/IcyEr01h7ZuRMRg4xN2OCsFPtYIgsvu69uChKh9duMRKSPY1I1h6v8uBwmVxftfzfpw1/o4u4X",
	"ExPjfeYi9U16e8dpvqTPvqV3m2T5J1pfJtfW2Z35amad7Nyhuz+LpcKWvTrvpcDB5X3ESHZXu79cZTOn",
	"JHFwf0r6HSOUkcTY2Njvc3hEYr+yEl1bJNc2yL1t582ys9GgO49Jq+VOJ7KS6/H6fXLQFue5c8n+HI9G",
	"t7XjTqR6E/bGrrO72dskIB2u3HOaTQ+kJIOU7EFKckiCFMmbhcONA7K51W0vxd3JRG/S2V8g37UP7y2T",
	"+r47xwC9unf46EG3tejsP3dHU1mJMVG3tUh3Htsbu+5wOivZnTbZ+55ZWasJf7tzmd4Fuq1FsnOXrG33",
	"XM3ziP48Z29fz+FEVBJBdvau2Cvb0uT4l+cnpBjUlJgmuoExKbmO47ktDs3hZHTgdPq4RtefuOHebdBb",
	"L2ht66hTZ+A2n4lx580Kuf9j32cM/VHnag6not7g3Xc2GsLC4b8aTnPuqFMX/5DNZ86LJ4erN+haO3hU",
	"fIQ5iZ+XjkpihwgZqd8Ty90w36yTVoNB6+1mCcI3ZnqeoCv7pL7nXU+Wl446DeE4afLzz3wOis2aOsQG",
	"lBk55KehMV2dlJxXu07zEV1/4jQfCWM5DCKgpMjIFQwu3/z17ATjF1MxSz76ARFwGemGYJxENB6Niz6F",
	"MNQUkAWpaDyaEkQ6zftzjI/PgksopJWJIPkKpV+HnEKRDtniswWQBV8gWDKnP5lG8t8B410hdLiZZDze",
	"o04k5ADUtJIi880xRkJszO0KfMc0P4y3csMql6FeYYD66cOrld0Wliw0UE9CEvUEkGFC0zJYO7BkGRkG",
	"4/FqxBVkbMFHOiqCLDgVGyi2mCvXYl6txrf5fOODwg/uAyVzT0j7pfAf60rwksH6j1ExTFQGF9nioXzg",
	"wkk1QmLgFtLtvX6rEGnGaLMHodtZJQv1Y0qqYc/vk+WlsEJpDKU9W+NPe063gUB/oiNoIlf6AdFikWH+",
	"SS1UThjlKWgoct71wnCsyXqbrj0VV7Y3293X173h7unKvlQcaD9XrXFxNqy5BjrqpKpoIHFOIlhOkG0+",
	"HV0dVi5Molffu6R6VTBcUjz49MZyt70pIh9aVYnQqpoqK6aJCizDfXTGHcUik0gkEslkMplKpVLpdDqd",
	"yWQyo6Ojo6dPnz79rhB8yHoN9nPGjOkTO7GsGIaCL+WLCioVfL4MdtagL1kXG/jSf5pPovb0KL/PZVhS",
	"ChxYvgiVEssLr3FvaYvWF2q8T49Mu3049wpdJ1SOnwsDUsDDiH0CDHJiSI88vlNxBellLtFGXUHGWxb5",
	"oeE0f6JXFsJ61+fInBhYOy9SnFW+DsvIRDoDOxueUqLf1zqAPRFAlvdW9sAWvTpQGP669gZg6FkEk1Mp",
	"OV3IoNHiu7kmpHje8lw65rF08b0pRlZxUdHL4bnp1cWhJJMaFEaAznUkI+UyKuR7zA8S8Xg0AyIDPhoY",
	"5wmqIVxg9wpB0pfuoTCSIVzXO+yDM5LITZGtXl5Kn9DzWDXzRdXCoZ7vC/pQSsj8NpTgNz3MCsH6fDsr",
	"WAbSjdgs+8PZERlGNVZSyooQT29hBfZ0qb8UHyFccc1H+oJKvKZ+rc2RvdeHV7bp/Rf09h59usGewT80",
	"6FqNbG4Fp45hka8MpP9FwHoHfXhxhXOH97L/PW/8lp9T3p8hjslT4Yy35GliuG/CmbwJlVLe0grQZCfH",
	"I72syfOs4COeJXlZ0EZcJHioFPIW4YkBJIII+uI0BEfmAzKJcJ5I4yCTnDRCCuZKo594wz6685DeqrvF",
	"1PuaF6SU0f+7yvBWtvCEh084ebgyykD65fCC/BRdRiVVYxGUxCoQAZZeAlkwbZpaNhYrqTIsTauGmR2L",
	"M3ka8R8xrqsFizf7sBOMbIxx2Yip4IoGK1FNRwVFNrUSrERnKv8ArLxcyMEPvfwddL9FFp6Lj3sDmuiz",
	"ZRCPy3Sh24RTQvY8P7AP1sP3uA/Y6sXqfwIAAP//rfK0bOYYAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}

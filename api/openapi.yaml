openapi: 3.0.3
info:
  title: TinyPay API
  description: |
    基于 RESTful 规范的支付接口，支持 Aptos 区块链交易处理。

    ## 响应格式说明
    所有接口使用统一的响应格式：
    ```json
    {
      "code": 数字,
      "data": { /* 具体数据或null */ }
    }
    ```
    前端根据HTTP状态码判断请求是否成功，根据code字段判断具体的业务状态。

    ## 业务状态码定义
    ### 成功状态码 (1000-1999)
    - 1000: 服务器运行正常
    - 1001: 交易创建成功
    - 1002: 交易处理中
    - 1003: 交易确认成功

    ### 错误状态码 (2000-2999)
    - 2000: 金额必须大于0
    - 2001: 金额超出限制
    - 2002: 余额不足
    - 2003: OPT 不正确
    - 2004: 缺少必需字段
    - 2005: 交易不存在

    ## 使用流程
    1. 前端调用 `POST /api/payments` 创建支付交易
    2. 服务器检查字段完整性（缺失字段返回状态码2004）
    3. 服务器进行交易验证（验证失败随机返回状态码2000-2003）
    4. 验证成功则返回交易哈希（状态码1001）
    5. 前端收到交易哈希后，使用 `GET /api/payments/{transaction_hash}` 轮询查询状态
  version: 1.0.0
  contact:
    name: TinyPay API Support
  license:
    name: MIT
servers:
  - url: http://localhost:9090
    description: Development server
  - url: https://api-tinypay.predictplay.xyz
    description: Production server

paths:
  /api:
    get:
      summary: 健康检查
      description: 检查服务器运行状态。
      operationId: healthCheck
      tags:
        - system
      responses:
        '200':
          description: 服务器正常
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                healthy:
                  summary: 服务器正常
                  value:
                    code: 1000
                    data:
                      status: "success"
  /api/payments:
    post:
      summary: 创建支付交易
      description: |
        创建新的支付交易，服务器会先检查字段完整性，然后进行交易验证，验证成功后返回交易哈希。
      operationId: createPayment
      tags:
        - payments
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentRequest'
            examples:
              basic_payment:
                summary: 基本支付示例
                value:
                  payer_addr: "0x1234567890abcdef1234567890abcdef12345678"
                  opt: "deadbeef"
                  payee_addr: "0xabcdef1234567890abcdef1234567890abcdef12"
                  amount: 1000000
                  currency: "APT"
      responses:
        '200':
          description: 交易创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: 成功提交交易
                  value:
                    code: 1001
                    data:
                      status: "submitted"
                      transaction_hash: "0x00001111222233334444555566667777abcdef1234567890abcdef1234567890"
        '400':
          description: 请求错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                missing_fields:
                  summary: 缺少必需字段
                  value:
                    code: 2004
                    data:
                      missing_fields: ["payer_addr", "amount"]
                validation_failed:
                  summary: 交易验证失败
                  value:
                    code: 2000
                    data: null

  /api/payments/{transaction_hash}:
    get:
      summary: 查询交易状态
      description: 根据交易哈希查询交易状态和详情。
      operationId: getTransactionStatus
      tags:
        - payments
      parameters:
        - name: transaction_hash
          in: path
          required: true
          description: 交易哈希值
          schema:
            type: string
            pattern: '^0x[a-fA-F0-9]{64}$'
            example: "0x1a2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890"
      responses:
        '200':
          description: 交易状态查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                pending:
                  summary: 交易处理中
                  value:
                    code: 1002
                    data:
                      status: "pending"
                confirmed:
                  summary: 交易确认成功
                  value:
                    code: 1003
                    data:
                      status: "confirmed"
                      received_amount: "100.5"
                      currency: "APT"
        '404':
          description: 交易不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                not_found:
                  summary: 交易不存在
                  value:
                    code: 2005
                    data: null

  /api/users/{user_address}/limits:
    get:
      summary: 查询用户限制
      description: 根据用户地址查询用户的支付限制、尾部更新次数和最大尾部更新次数。
      operationId: getUserLimits
      tags:
        - users
      parameters:
        - name: user_address
          in: path
          required: true
          description: 用户地址
          schema:
            type: string
            pattern: '^0x[a-fA-F0-9]{1,64}$'
            example: "0x1234567890abcdef1234567890abcdef12345678"
      responses:
        '200':
          description: 用户限制查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: 查询成功
                  value:
                    code: 1004
                    data:
                      payment_limit: 1000000
                      tail_update_count: 5
                      max_tail_updates: 10
                not_found:
                  summary: 用户不存在
                  value:
                    code: 1004
                    data:
                      payment_limit: 0
                      tail_update_count: 0
                      max_tail_updates: 0
        '400':
          description: 请求错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_address:
                  summary: 无效地址格式
                  value:
                    code: 2006
                    data: null

components:
  schemas:
    ApiResponse:
      type: object
      required:
        - code
      properties:
        code:
          type: integer
          description: 业务状态码
          example: 1000
        data:
          type: object
          description: 响应数据
          nullable: true
    PaymentRequest:
      type: object
      required:
        - payer_addr
        - opt
        - payee_addr
        - amount
      properties:
        payer_addr:
          type: string
          description: 付款地址 hex格式
          pattern: '^0x[a-fA-F0-9]{1,64}$'
          example: "0x1234567890abcdef1234567890abcdef12345678"
        opt:
          type: string
          description: OPT hex格式
          pattern: '^[a-fA-F0-9]+$'
          example: "deadbeef"
        payee_addr:
          type: string
          description: 收款地址 hex格式
          pattern: '^0x[a-fA-F0-9]{1,64}$'
          example: "0xabcdef1234567890abcdef1234567890abcdef12"
        amount:
          type: integer
          format: int64
          minimum: 1
          description: 金额 uint类型
          example: 1000000
        currency:
          type: string
          description: 货币种类
          default: "APT"
          enum: ["APT", "USDC"]
          example: "APT"

tags:
  - name: payments
    description: 支付相关接口
  - name: users
    description: 用户相关接口
  - name: system
    description: 系统相关接口
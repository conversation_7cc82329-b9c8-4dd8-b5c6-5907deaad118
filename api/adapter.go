package api

import (
	"fmt"
	"log"
	"net/http"
	"sync"
	"tinypay-server/utils"

	"tinypay-server/client"

	"github.com/gin-gonic/gin"
)

// APIServer implements the ServerInterface generated by oapi-codegen
type APIServer struct {
	aptosClient *client.AptosClient
	payerLocks  map[string]*sync.Mutex
	locksMutex  sync.RWMutex
}

// NewAPIServer creates a new API server instance
func NewAPIServer(aptosClient *client.AptosClient) *APIServer {
	return &APIServer{
		aptosClient: aptosClient,
		payerLocks:  make(map[string]*sync.Mutex),
		locksMutex:  sync.RWMutex{},
	}
}

// getPayerLock returns the mutex for a specific payer address, creating one if it doesn't exist
func (s *APIServer) getPayerLock(payerAddr string) *sync.Mutex {
	// First try to get the lock with read lock
	s.locksMutex.RLock()
	if lock, exists := s.payerLocks[payerAddr]; exists {
		s.locksMutex.RUnlock()
		return lock
	}
	s.locksMutex.RUnlock()

	// If lock doesn't exist, acquire write lock and create it
	s.locksMutex.Lock()
	defer s.locksMutex.Unlock()

	// Double-check in case another goroutine created it while we were waiting
	if lock, exists := s.payerLocks[payerAddr]; exists {
		return lock
	}

	// Create new lock for this payer
	lock := &sync.Mutex{}
	s.payerLocks[payerAddr] = lock
	return lock
}

// HealthCheck implements the health check endpoint
func (s *APIServer) HealthCheck(c *gin.Context) {
	data := map[string]interface{}{
		"status": "success",
	}

	response := CreateApiResponseWithMap(CodeServerHealthy, data)
	c.JSON(http.StatusOK, response)
}

// CreatePayment implements the payment creation endpoint
func (s *APIServer) CreatePayment(c *gin.Context) {
	var req PaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Invalid request body format
		response := CreateApiResponseWithNullData(CodeInvalidOpt)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// Early validation to get payer address for locking
	if req.PayerAddr == "" {
		missingFields := []string{"payer_addr"}
		data := map[string]interface{}{
			"missing_fields": missingFields,
		}
		response := CreateApiResponseWithMap(CodeMissingFields, data)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// Acquire lock for this specific payer to prevent concurrent payments
	payerLock := s.getPayerLock(req.PayerAddr)
	payerLock.Lock()
	defer payerLock.Unlock()

	// Check for missing fields after successful JSON binding (PayerAddr already validated above)
	missingFields := []string{}
	if req.Opt == "" {
		missingFields = append(missingFields, "opt")
	}
	if req.PayeeAddr == "" {
		missingFields = append(missingFields, "payee_addr")
	}
	if req.Amount == 0 {
		missingFields = append(missingFields, "amount")
	}

	if len(missingFields) > 0 {
		data := map[string]interface{}{
			"missing_fields": missingFields,
		}
		response := CreateApiResponseWithMap(CodeMissingFields, data)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// Handle currency type - default to APT if not specified
	currency := "APT"
	if req.Currency != nil {
		currency = string(*req.Currency)
	}

	// Get coin type from currency mapping
	coinType, err := utils.GetCoinType(s.aptosClient.GetConfig(), currency)
	if err != nil {
		log.Printf("Unsupported currency: %s", currency)
		response := CreateApiResponseWithNullData(CodeInvalidOpt)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	log.Printf("Processing payment with currency: %s, coin type: %s", currency, coinType)

	// Convert hex strings to bytes
	optBytes := utils.HexToASCIIBytes(req.Opt)
	log.Printf("\nAptos CLI format for opt:\n")
	fmt.Printf("u8:[")
	for i, b := range optBytes {
		if i > 0 {
			fmt.Printf(",")
		}
		fmt.Printf("%d", b)
	}
	fmt.Printf("]\n")

	// Convert amount to uint64
	amount := uint64(req.Amount)

	// Check amount validation
	if amount <= 0 {
		response := CreateApiResponseWithNullData(CodeAmountMustBePositive)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// Compute payment hash for the transaction
	// note: 暂时不用商家自行处理 precommit
	//commitHash, err := s.aptosClient.ComputePaymentHash(req.PayerAddr, req.PayeeAddr, amount, optBytes)
	//if err != nil {
	//	log.Printf("Failed to compute payment hash: %v", err)
	//	response := CreateApiResponseWithNullData(CodeInvalidOpt)
	//	c.JSON(http.StatusBadRequest, response)
	//	return
	//}

	// Submit the transaction with coin type support
	txHash, err := s.aptosClient.CompletePaymentWithCoinType(optBytes, req.PayerAddr, req.PayeeAddr, amount, []byte(""), coinType)
	if err != nil {
		log.Printf("Failed to complete payment: %v", err)
		// todo:
		// Randomly return one of the validation error codes (2000-2003)
		errorCodes := []int{CodeAmountMustBePositive, CodeAmountExceedsLimit, CodeInsufficientBalance, CodeInvalidOpt}
		randomCode := errorCodes[len(err.Error())%len(errorCodes)]
		response := CreateApiResponseWithNullData(randomCode)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	data := map[string]interface{}{
		"status":           "submitted",
		"transaction_hash": txHash,
		"currency":         currency,
		"coin_type":        coinType,
	}
	response := CreateApiResponseWithMap(CodeTransactionCreated, data)
	c.JSON(http.StatusOK, response)
}

// GetTransactionStatus implements the transaction status query endpoint
func (s *APIServer) GetTransactionStatus(c *gin.Context, transactionHash string) {
	if transactionHash == "" {
		response := CreateApiResponseWithNullData(CodeTransactionNotFound)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// Get detailed transaction information
	txInfo, err := s.aptosClient.GetTransactionDetails(transactionHash)
	if err != nil {
		response := CreateApiResponseWithNullData(CodeTransactionNotFound)
		c.JSON(http.StatusNotFound, response)
		return
	}

	if !txInfo.Confirmed {
		data := map[string]interface{}{
			"status": "pending",
		}
		response := CreateApiResponseWithMap(CodeTransactionPending, data)
		c.JSON(http.StatusOK, response)
		return
	}

	if txInfo.Success {
		// Convert amount from octas to APT (1 APT = 100,000,000 octas)
		// amountInAPT := float64(txInfo.Amount) / 100000000.0
		data := map[string]interface{}{
			"status":          "confirmed",
			"received_amount": txInfo.Amount,
			"currency":        txInfo.CoinType,
		}
		response := CreateApiResponseWithMap(CodeTransactionConfirmed, data)
		c.JSON(http.StatusOK, response)
	} else {
		data := map[string]interface{}{
			"status": "failed",
			"error":  txInfo.Error,
		}
		response := CreateApiResponseWithMap(CodeTransactionConfirmed, data)
		c.JSON(http.StatusOK, response)
	}
}

// GetUserLimits implements the GET /api/users/{user_address}/limits endpoint
func (s *APIServer) GetUserLimits(c *gin.Context, userAddress string) {
	log.Printf("Getting user limits for address: %s", userAddress)

	// Validate user address format
	if userAddress == "" {
		response := CreateApiResponseWithNullData(CodeInvalidOpt)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// Call the contract view function
	userLimits, err := s.aptosClient.GetUserLimits(userAddress)
	if err != nil {
		log.Printf("Failed to get user limits: %v", err)
		response := CreateApiResponseWithNullData(CodeInvalidOpt)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// Return successful response
	data := map[string]interface{}{
		"user_limits": userLimits,
	}
	response := CreateApiResponseWithMap(CodeServerHealthy, data)
	c.JSON(http.StatusOK, response)
}
